#!/bin/bash

# Odoo 14.0 Deployment Script
# Generated on: $(date)

echo "==================================="
echo "  Odoo 14.0 Deployment Script"
echo "==================================="

# Configuration
ODOO_USER="odoo"
ODOO_HOME="/opt/odoo"
ODOO_VERSION="14.0"
BACKUP_DIR="/home/<USER>/odoo-backup"

# Colors for output
RED="\033[0;31m"
GREEN="\033[0;32m"
YELLOW="\033[1;33m"
NC="\033[0m" # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Install Dependencies
install_dependencies() {
    print_status "Installing system dependencies..."
    sudo apt update
    sudo apt install -y python3 python3-pip python3-venv postgresql postgresql-contrib
    sudo apt install -y python3-dev libxml2-dev libxslt1-dev libldap2-dev libsasl2-dev
    sudo apt install -y libtiff5-dev libjpeg8-dev libopenjp2-7-dev zlib1g-dev libfreetype6-dev
    sudo apt install -y libpq-dev python3-psycopg2 wkhtmltopdf
}

# Step 2: Create Odoo User
create_odoo_user() {
    print_status "Creating Odoo user..."
    sudo adduser --system --home=$ODOO_HOME --group $ODOO_USER
}

# Step 3: Setup PostgreSQL
setup_postgresql() {
    print_status "Setting up PostgreSQL..."
    sudo -u postgres createuser -s $ODOO_USER
    sudo -u postgres createdb -O $ODOO_USER odoo_production
}

# Step 4: Extract Backups
extract_backups() {
    print_status "Extracting backups..."
    
    # Extract Odoo core
    sudo tar -xzf odoo-14.0-core.tar.gz -C $ODOO_HOME/
    
    # Extract custom addons
    sudo tar -xzf platform-odex25-light.tar.gz -C $ODOO_HOME/
    
    # Extract data directory
    sudo tar -xzf prod_data_dir.tar.gz -C $ODOO_HOME/
    
    # Copy configuration
    sudo cp odoo-server.conf /etc/
    
    # Set permissions
    sudo chown -R $ODOO_USER:$ODOO_USER $ODOO_HOME/
}

# Step 5: Install Python Dependencies
install_python_deps() {
    print_status "Installing Python dependencies..."
    sudo -u $ODOO_USER python3 -m venv $ODOO_HOME/odoo-venv
    sudo -u $ODOO_USER $ODOO_HOME/odoo-venv/bin/pip install --upgrade pip
    sudo -u $ODOO_USER $ODOO_HOME/odoo-venv/bin/pip install -r $ODOO_HOME/odoo-14.0/requirements.txt
}

# Step 6: Create Systemd Service
create_service() {
    print_status "Creating systemd service..."
    sudo tee /etc/systemd/system/odoo.service > /dev/null <<EOL
[Unit]
Description=Odoo
Documentation=http://www.odoo.com
After=network.target postgresql.service

[Service]
Type=simple
SyslogIdentifier=odoo
PermissionsStartOnly=true
User=$ODOO_USER
Group=$ODOO_USER
ExecStart=$ODOO_HOME/odoo-venv/bin/python3 $ODOO_HOME/odoo-14.0/odoo-bin -c /etc/odoo-server.conf
StandardOutput=journal+console

[Install]
WantedBy=multi-user.target
EOL

    sudo systemctl daemon-reload
    sudo systemctl enable odoo
}

# Main execution
main() {
    print_status "Starting Odoo 14.0 deployment..."
    
    install_dependencies
    create_odoo_user
    setup_postgresql
    extract_backups
    install_python_deps
    create_service
    
    print_status "Deployment completed!"
    print_warning "Please update /etc/odoo-server.conf with your specific settings"
    print_status "Start Odoo with: sudo systemctl start odoo"
    print_status "Check status with: sudo systemctl status odoo"
}

# Run main function
main
